
.content-weapon
{
 // margin: 25px;
  margin-top: 10px;
  margin-left: 25px;
  margin-right: 25px;
}

/* Fixed min-height for header in this component only */
.list-header-row {
  min-height: 105px;
}

.iconInter {
  text-align: center; 
  font-size: 30px !important; 
  margin-top: 8px;
}
i:hover{
  color: red;
  cursor: pointer;
}

.justPass {
  position: relative;
  top: -37px;
  width: 30%;
  float: right;
}

/* Fundo escurecido (backdrop) */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* <PERSON>e ser menor que o modal */
}


/*Modal do sistema */
.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: rgb(230, 230, 230);
    z-index: 150;
}

.popup-report
{ 
  border-radius: 8px;
  width: 480px;
  position: fixed;
 // left: 35%;
  padding: 24px;
  top: 23%;
 // transform: translate(-60%, -50%);
 transform: translate(-10%, -40%);
  z-index: 1000;
  opacity: 1;  

}
.modal-header {
  color: white !important;
  padding: 0px !important;

  .modal-title {
    text-align: left !important;
    margin-bottom: 7px;
    .close {
      opacity: 0px !important;
      margin-top: 0px !important;
    }
  }

   button {
    span {
      color: white !important;
    }
   }
}

.close {
  opacity: 1 !important;
 }

 .contextInfo {
  color: white;
  text-align: left;
  overflow-y: auto;
  white-space: pre-wrap;
  height: auto;
  max-height: 700px;
  scrollbar-width: thin;
  scroll-behavior: auto;  
  scrollbar-color: white black;
  padding-top: 10px;
}
.background-div {	
  position: relative;
  display: flex;
  justify-content: center;
  z-index: 9999;
}	

.background-div.popup-open:before 	
{	
  content: "";	
  position: fixed;	
  top: 0;	
  left: 0;	
  width: 100%;	
  height: 100%;	
  background-color: rgba(0, 0, 0, 0.5);	
  z-index: 9998;	
  pointer-events: none;	
}	

// FIM DO MODAL

/* Header Layout for Weapon Type Indicator */
.header-content {
  position: relative;
  width: 100%;
}



.weapon-type-header-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  z-index: 10;
  pointer-events: none; /* Allow clicks to pass through to buttons behind */
}

.weapon-type-header-badge {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

/* WL Range Color Classes for Weapon Type Badge */
.weapon-type-wl-green {
  background: rgb(0, 176, 80);
  color: white;
  border-color: rgb(0, 156, 70);
}

.weapon-type-wl-blue {
  background-color: #0070c0;
  color: white;
  border-color: #0060a0;
}

.weapon-type-wl-purple {
  background: rgb(112, 48, 160);
  color: white;
  border-color: rgb(92, 28, 140);
}

.weapon-type-wl-yellow {
  background: rgb(255, 192, 0);
  color: white;
  border-color: rgb(235, 172, 0);
}

.weapon-type-wl-default {
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  color: #5f6368;
  border-color: #dadce0;
}
