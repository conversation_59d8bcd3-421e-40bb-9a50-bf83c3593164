<div class="content-weapon" *ngIf="!custom?.selectedWeaponId">
  <h2>No weapon selected!</h2>
</div>
<div class="content-weapon" *ngIf="custom?.selectedWeaponId">
  <div class="card list-header-row">
    <app-header-with-buttons class="card" [isBackButtonEnabled]="true"
                             (cardBackButtonClick)="onBack()"
                             [cardTitle]="currentCharacter?.name"
                             [cardDescription]="currentCharacter?.description"
                             [rightButtonTemplates]="[leftButtonTemplate,rightButtonTemplate]">
    </app-header-with-buttons>
  </div>
  <div class="card list-header"
       style="height: 70px; margin-bottom: 0px; text-align: end; overflow-x: auto;">
    <div class="header">

      <div *ngIf="activeTab === 'passive'" style="display: flex; justify-content: center;">
        <i ngClass="iconInter" (click)="onModalClick()"class="pe-7s-info batt"></i>
      </div>
      
          <!--Modal Info--> 
          <ng-container *ngIf="isModalInfo">              
            <div class="background-div handleOut" aria-hidden="true" >
              <div class="modal-backdrop" *ngIf="isModalInfo"></div>        
                <div id="modal-close" @popup class="popup-report" (mouseleave)="isModalInfo = false" style="background-color: black;">                
                    <div class="modal-header">                      
                          <div style="display: flex; justify-content: space-between;">
                            <p style="color:azure !important; text-align: center;" class="modal-title">RULES</p>
                            <button type="button" class="close handleOut" (click)="closeAreaStatsPopup()" data-dismiss="background-div" aria-label="Fechar"> 
                              <span aria-hidden="true">&times;</span>                               
                            </button> 
                           </div>                 
                              </div>                          
                          <div class="contextInfo">
                            <p>All Weapons:</p> 
                            <h5 style="color:azure !important; text-align: left; text-transform: none !important;" class="modal-title title">
                              1.	This is the primary rule. After a selection, the chosen INDEX is removed from the set of available options.
                           </h5>
                           <br>
                           <p>Common Weapons: </p>
                           <h5 style="color:azure !important; text-align: left; text-transform: none !important;" class="modal-title title">
                            2.	Only allow the selection of a single INDEX (ID1 or ID1+ID2). These weapons consist of either a single bonus or a bonus + condition.
                         </h5>
                         <br>
                         <p>Special Weapons:</p>
                         <h5 style="color:azure !important; text-align: left; text-transform: none !important;" class="modal-title title">
                          3.	Allow the selection of multiple INDEX (ID1 or ID1+ID2).
                        </h5>
                          <h5 style="color:azure !important; text-align: left; text-transform: none !important;" class="modal-title title">                      
                            4.	For the same weapon, the selection of INDEX containing the same ID1 must be prevented.
                         </h5>
                       
                    </div>    
                  </div>           
               </div>
          </ng-container>
          <!--Fim do Modal-->

      <div [ngClass]="{'justPass': activeTab === 'passive'}" class="header-content">
        <button class="{{activeTab === 'general' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('general')" style="margin-left: 2px;">
          1 - General
        </button>
        <button class="{{activeTab === 'improve' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('improve')" style="margin-left: 2px;">
          2 - Improve
        </button>
        <button class="{{activeTab === 'passive' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('passive')" style="margin-left: 2px;">
          3 - Passive Skill
        </button>

        <!-- Weapon Type Indicator in Header Center -->
        <div class="weapon-type-header-container">
          <div class="weapon-type-header-badge weapon-type-wl-default">
            {{ getWeaponType() || 'Debug: No weapon type detected' }}
          </div>
        </div>
      </div>

    </div>
  </div>


  <app-item-details-report-general #query [weaponId]="characterFromQuery"
                                   *ngIf="activeTab === 'general'">
  </app-item-details-report-general>
  <app-item-details-report-improve #query [weaponId]="characterFromQuery"
                                   *ngIf="activeTab === 'improve'">
  </app-item-details-report-improve>
  <app-item-details-report-passive #query [weaponId]="characterFromQuery"
                                   *ngIf="activeTab === 'passive'">
  </app-item-details-report-passive>

</div>
