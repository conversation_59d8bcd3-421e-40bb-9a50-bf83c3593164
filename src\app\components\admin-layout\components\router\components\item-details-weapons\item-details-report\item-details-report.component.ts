import { Component, OnInit, QueryList, ViewChildren } from '@angular/core';
import { Item, Weapon } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemService, WeaponService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { CommonWeaponsService } from 'src/app/services/commonWeaponsService';
import { SpecialWeaponService } from 'src/app/services/special-weapon.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-item-details-report',
  templateUrl: './item-details-report.component.html',
  styleUrls: ['./item-details-report.component.scss'],
})
export class ItemDetailsReportComponent implements OnInit 
{
  @ViewChildren('query') childGames: QueryList<ICollectibleDetails>;
  public activeTab: string;
  islinedUp:boolean;
  sortNameOrder = -1;
  custom: Custom;
  item: Item;
  characterFromQuery = '';
  collectibleFromQueryList = [];
  itemListLineup = [];
  characterListIndex = 0;
  initialIndex = 0;
  currentCharacter : Item;
  isModalInfo = false;
  weapon: Weapon;

  constructor(
    protected _customService: CustomService,
    protected _itemService: ItemService,
    private _router: Router,
    private _activatedRoute : ActivatedRoute,
    private _weaponService : WeaponService,
    private _commonWeaponsService: CommonWeaponsService,
    private _specialWeaponService: SpecialWeaponService,
    private _itemClassService: ItemClassService
  )
  {}

  async ngOnInit(): Promise<void>
  {
    await this._itemService.toFinishLoading();
    await this._customService.toFinishLoading();
    this.characterFromQuery = this._weaponService.currentSelectedCharacter;
    //this.collectibleFromQueryList = await this._activatedRoute.snapshot.queryParams['collectibles'];
    this.collectibleFromQueryList = await this._activatedRoute.snapshot.queryParams['itemListLineup'];   
    this.custom = await this._customService.svcGetInstance();
    this.item = this._itemService.svcFindById(this.custom.selectedWeaponId);
    this.currentCharacter = this._itemService.svcFindById(this.characterFromQuery); 
    
   
    for(let i = 0; i < this.collectibleFromQueryList.length; i++)
    {//compares the position of the id
      if(this.collectibleFromQueryList[i] == this._weaponService.currentSelectedCharacter)
      {
        this.initialIndex = i;
        this.characterListIndex = i;
        break;
      }
    }  
    const tab = localStorage.getItem(`tab-ItemDetailsReportComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'class-selection' : tab;
    this.resetAll();
    this.switchToTab('general');
  }

   public async switchToTab(tab: string) 
  {
    this.activeTab = tab;
    localStorage.setItem(`tab-ItemDetailsReportComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }

  public onBack()
  {
    this._router.navigate(['weaponRecords']);
  }

  public readonly leftButtonTemplate: Button.Templateable = 
  {
    title: 'Back to last character',
    onClick: this.leftButton.bind(this),
    iconClass: 'pe-7s-angle-left',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  public readonly rightButtonTemplate: Button.Templateable = 
  {
    title: 'Go to next character',
    onClick: this.rightButton.bind(this),
    iconClass: 'pe-7s-angle-right',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  //back
leftButton() {
  // Se o índice atual for 0, vai para o último item da lista
  if (this.characterListIndex === 0) {
    this.characterListIndex = this.collectibleFromQueryList.length - 1;
  } else {
    // Caso contrário, decrementa o índice
    this.characterListIndex = this.characterListIndex - 1;
  }

  // Atualiza o personagem atual
  let nextCharacter = this.collectibleFromQueryList[this.characterListIndex];
  this.currentCharacter = this._itemService.svcFindById(nextCharacter);
  this._weaponService.currentSelectedCharacter = nextCharacter;
  this._customService.currentSelectedItem = nextCharacter;
  this.resetAll();
}

//next
rightButton() {
  // Se o índice atual for o último da lista, volta para o primeiro item
  if (this.characterListIndex === this.collectibleFromQueryList.length - 1) {
    this.characterListIndex = 0;
  } else {
    // Caso contrário, incrementa o índice
    this.characterListIndex = this.characterListIndex + 1;
  }

  // Atualiza o personagem atual
  let nextCharacter = this.collectibleFromQueryList[this.characterListIndex];
  this.currentCharacter = this._itemService.svcFindById(nextCharacter);
  this._weaponService.currentSelectedCharacter = nextCharacter;
  this._customService.currentSelectedItem = nextCharacter;
  this.resetAll();
}

  //For more information look the method name on documentation.
  resetAll() 
  { 
    this.childGames.forEach(c => c.reset(this._weaponService.currentSelectedCharacter));
  }
  
  closeAreaStatsPopup() {
    this.isModalInfo =  false;
  }
  //modal
  onModalClick(): void {
    this.isModalInfo = !this.isModalInfo;
  }

  /**
   * Gets the current weapon from the weapon service
   */
  private getCurrentWeapon(): Weapon | null {
    const weaponId = this._customService.currentSelectedItem;
    if (!weaponId) return null;

    return this._weaponService.svcFindById(weaponId);
  }

  /**
   * Determines if the current weapon is a common weapon or special weapon
   * @returns 'Arma Comum' | 'Arma Especial' | null
   */
  getWeaponType(): string | null {
    const weapon = this.getCurrentWeapon();
    if (!weapon?.itemId) {
      return null;
    }

    // Check if weapon exists in CommonWeapons service
    const isCommonWeapon = this._commonWeaponsService.models.some(cw =>
      cw.commonWeaponReceivedHC?.some(cwHC => cwHC.idNameHC === weapon.itemId)
    );

    if (isCommonWeapon) {
      return 'Arma Comum';
    }

    // Check if weapon exists in SpecialWeapons service
    const isSpecialWeapon = this._specialWeaponService.models.some(sw =>
      sw.itemId === weapon.itemId
    );

    if (isSpecialWeapon) {
      return 'Arma Especial';
    }

    // Fallback: Check by item class
    const item = this._itemService.svcFindById(weapon.itemId);
    if (item) {
      // Find which item class this item belongs to
      const itemClass = this._itemClassService.models.find(ic =>
        ic.itemIds?.includes(item.id)
      );

      if (itemClass) {
        if (itemClass.name === 'ARMAS COMUNS') {
          return 'Arma Comum';
        } else if (itemClass.name === 'ARMAS ESPECIAIS' || itemClass.name === 'BLUEPRINTS') {
          return 'Arma Especial';
        }
      }
    }

    return null;
  }

  /**
   * Gets the WL Range class based on the weapon's HC value
   * @returns CSS class string for WL Range colors
   */
  getWeaponTypeWLRangeClass(): string {
    const weapon = this.getCurrentWeapon();
    if (!weapon?.itemId) {
      return '';
    }

    // Get HC value from CommonWeapons service
    let hc = 0;
    const commonWeapon = this._commonWeaponsService.models.find(cw =>
      cw.commonWeaponReceivedHC?.some(cwHC => cwHC.idNameHC === weapon.itemId)
    );

    if (commonWeapon) {
      const weaponHC = commonWeapon.commonWeaponReceivedHC?.find(cwHC => cwHC.idNameHC === weapon.itemId);
      if (weaponHC) {
        // Use the HC from the common weapon data
        hc = commonWeapon.hc || 0;
      }
    }

    // Apply the same WL Range logic as in the weapons components
    const roundedHc = Math.floor(hc);

    if (roundedHc >= 0 && roundedHc <= 2) {
      return 'weapon-type-wl-green'; // WL Range 1-6
    } else if (roundedHc >= 3 && roundedHc <= 5) {
      return 'weapon-type-wl-blue'; // WL Range 7-12
    } else if (roundedHc >= 6 && roundedHc <= 7) {
      return 'weapon-type-wl-purple'; // WL Range 13-16
    } else if (roundedHc >= 8 && roundedHc <= 9) {
      return 'weapon-type-wl-yellow'; // WL Range 17-20
    } else {
      return 'weapon-type-wl-default';
    }
  }
}
